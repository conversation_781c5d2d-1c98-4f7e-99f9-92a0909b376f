# filename: generate_script.py

import openai
import os
from dotenv import load_dotenv

load_dotenv()
openai.api_key = os.getenv("OPENAI_API_KEY")

def generate_topic_and_script():
    topics_prompt = """
    Generate 5 fun, interesting, and educational video topics for 8-12 year old kids in simple language.
    Topics should be science, nature, space, or everyday curiosity based.
    Give only the titles in a numbered list.
    """

    topics_response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[{"role": "user", "content": topics_prompt}]
    )

    topics = topics_response["choices"][0]["message"]["content"].split("\n")
    scripts = []

    for topic in topics:
        if topic.strip():
            clean_title = topic.split(".")[1].strip() if "." in topic else topic.strip()
            script_prompt = f"""
            Write a cheerful, fun 300-word YouTube video script for kids aged 8-12 years on the topic: "{clean_title}". 
            Use simple language, short sentences, and end with a fun fact.
            """
            script_response = openai.ChatCompletion.create(
                model="gpt-4",
                messages=[{"role": "user", "content": script_prompt}]
            )
            script_text = script_response["choices"][0]["message"]["content"]
            scripts.append((clean_title, script_text))
            print(f"\n✅ Script Ready for: {clean_title}\n")
    
    return scripts

if __name__ == "__main__":
    scripts = generate_topic_and_script()
    for i, (title, script) in enumerate(scripts):
        with open(f"script_{i+1}_{title[:30].replace(' ', '_')}.txt", "w") as f:
            f.write(script)
