# filename: generate_script.py

import openai
import os
from dotenv import load_dotenv

load_dotenv()
openai.api_key = os.getenv("OPENAI_API_KEY")

def generate_single_topic_and_script():
    # ✅ 1 topic hi maang rahe hain
    topic_prompt = (
    "बच्चों के लिए 1 ऐसा मज़ेदार और imaginative YouTube Shorts टॉपिक सुझाइए, "
    "जैसे 'बच्चे मिट्टी से असली विमान बनाएंगे?' या 'उड़ने वाली गाय', जो सुनते ही बच्चों की curiosity जाग जाए। "
    "टॉपिक हिंदी (देवनागरी) में होना चाहिए।"
)

    topic_response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[{"role": "user", "content": topic_prompt}]
    )

    topic = topic_response["choices"][0]["message"]["content"].strip()
    topic_clean = topic.split(".")[1].strip() if "." in topic else topic

    # ✅ Script ke liye prompt
    script_prompt = (
    f"'{topic}' इस टॉपिक पर बच्चों के लिए एक प्यारी, मज़ेदार और शॉर्ट YouTube Shorts स्क्रिप्ट हिंदी (देवनागरी लिपि) में लिखिए। "
    "स्क्रिप्ट 100 शब्दों के अंदर हो और इसमें शामिल हो:\n\n"
    "1. 🎬 **शुरुआत:** ‘अरे बच्चों, सोचो ज़रा अगर...’ जैसी engaging शुरुआत\n"
    "2. 🧚 **कल्पनात्मक कहानी:** बच्चे कुछ मज़ेदार, अजीब या जादुई करते हैं\n"
    "3. 😲 **ट्विस्ट या मज़ा:** कुछ funny या surprising मोड़\n"
    "4. 😊 **अंत:** ‘Like aur Subscribe करना मत भूलना बच्चों!’ के साथ प्यारा सा end\n\n"
    "भाषा आसान, प्यारी और बच्चों जैसी हो — जैसे कोई animated दोस्त या मस्तीभरा टीचर बोल रहा हो।"
)

    script_response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[{"role": "user", "content": script_prompt}]
    )

    script = script_response["choices"][0]["message"]["content"].strip()

    # ✅ Save to file
    os.makedirs("scripts/hi", exist_ok=True)
    filename = f"scripts/hi/script_{topic_clean[:30].replace(' ', '_')}.txt"
    with open(filename, "w", encoding="utf-8") as f:
        f.write(script)

    print(f"\n✅ Script generated for topic: {topic_clean}")
    print(f"📁 Saved to: {filename}\n")
    return topic_clean, script

# Run only once
if __name__ == "__main__":
    topic, script = generate_single_topic_and_script()
    print(f"\n🎯 TOPIC: {topic}\n")
    print(f"📜 SCRIPT:\n{script}")
