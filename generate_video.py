import os
import random
from moviepy.editor import *

SCRIPT_FOLDER = "scripts/hi"
AUDIO_FOLDER = "audio/hi"
VIDEO_FOLDER = "video_assets/bg_clips"
OUTPUT_FOLDER = "output"
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

def generate_video(script_path, audio_path, output_path, bg_video_path):
    with open(script_path, "r", encoding="utf-8") as f:
        script_text = f.read()

    audio = AudioFileClip(audio_path)
    bg_video = VideoFileClip(bg_video_path).subclip(0, audio.duration)
    bg_video = bg_video.set_audio(audio).resize(height=1080)

    # ✅ Safe crop
    if bg_video.w > 720:
        bg_video = bg_video.crop(x_center=bg_video.w/2, width=720)

    final_video = bg_video
    final_video.write_videofile(output_path, fps=24)
    print(f"✅ Video saved: {output_path}")

# 🔁 Loop on all files
for filename in os.listdir(SCRIPT_FOLDER):
    if filename.endswith(".txt"):
        base = filename.replace(".txt", "")
        script_path = os.path.join(SCRIPT_FOLDER, filename)
        audio_path = os.path.join(AUDIO_FOLDER, base + ".mp3")
        bg_video_path = random.choice([
            os.path.join(VIDEO_FOLDER, f)
            for f in os.listdir(VIDEO_FOLDER)
            if f.endswith(".mp4")
        ])
        output_path = os.path.join(OUTPUT_FOLDER, base + ".mp4")

        generate_video(script_path, audio_path, output_path, bg_video_path)

print("🎬 All videos generated!")
